// Configuration Google Analytics 4 pour Site Notaire
// Remplacer 'GA_MEASUREMENT_ID' par votre vrai ID Google Analytics

// Configuration des événements personnalisés
function setupGoogleAnalytics() {
    // Événement: Clic sur bouton "Prendre <PERSON>-vous"
    document.querySelectorAll('a[href="#contact"], .button--primary').forEach(button => {
        button.addEventListener('click', function() {
            gtag('event', 'clic_rendez_vous', {
                event_category: 'engagement',
                event_label: 'bouton_rendez_vous',
                value: 1
            });
        });
    });

    // Événement: Clic sur numéro de téléphone
    document.querySelectorAll('a[href^="tel:"]').forEach(link => {
        link.addEventListener('click', function() {
            gtag('event', 'clic_telephone', {
                event_category: 'contact',
                event_label: 'numero_telephone',
                value: 1
            });
        });
    });

    // Événement: Clic sur WhatsApp
    document.querySelectorAll('a[href*="wa.me"]').forEach(link => {
        link.addEventListener('click', function() {
            gtag('event', 'clic_whatsapp', {
                event_category: 'contact',
                event_label: 'whatsapp',
                value: 1
            });
        });
    });

    // Événement: Soumission formulaire de contact
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function() {
            gtag('event', 'soumission_formulaire', {
                event_category: 'conversion',
                event_label: 'formulaire_contact',
                value: 1
            });
        });
    }

    // Événement: Clic sur réseaux sociaux
    document.querySelectorAll('a[href*="facebook.com"], a[href*="linkedin.com"], a[href*="instagram.com"]').forEach(link => {
        link.addEventListener('click', function() {
            const platform = this.href.includes('facebook') ? 'facebook' : 
                           this.href.includes('linkedin') ? 'linkedin' : 'instagram';
            
            gtag('event', 'clic_reseau_social', {
                event_category: 'social',
                event_label: platform,
                value: 1
            });
        });
    });

    // Événement: Scroll profond (80% de la page)
    let scrollTracked = false;
    window.addEventListener('scroll', function() {
        if (!scrollTracked && (window.scrollY / document.body.scrollHeight) > 0.8) {
            gtag('event', 'scroll_profond', {
                event_category: 'engagement',
                event_label: '80_percent_scroll',
                value: 1
            });
            scrollTracked = true;
        }
    });

    // Événement: Temps passé sur la page (30 secondes)
    setTimeout(function() {
        gtag('event', 'temps_engagement', {
            event_category: 'engagement',
            event_label: '30_secondes',
            value: 1
        });
    }, 30000);
}

// Initialiser les événements quand le DOM est chargé
document.addEventListener('DOMContentLoaded', setupGoogleAnalytics);

// Configuration des objectifs de conversion
function setupConversionGoals() {
    // Objectif 1: Formulaire de contact soumis
    // Objectif 2: Clic sur numéro de téléphone
    // Objectif 3: Clic sur WhatsApp
    // Objectif 4: Visite de la page mentions légales (engagement)
    
    // Suivi des pages vues importantes
    const currentPage = window.location.pathname;
    
    if (currentPage.includes('mentions-legales')) {
        gtag('event', 'page_mentions_legales', {
            event_category: 'navigation',
            event_label: 'mentions_legales_vue',
            value: 1
        });
    }
    
    if (currentPage.includes('thank-you')) {
        gtag('event', 'page_remerciement', {
            event_category: 'conversion',
            event_label: 'formulaire_envoye',
            value: 1
        });
    }
}

// Initialiser les objectifs
document.addEventListener('DOMContentLoaded', setupConversionGoals);

// Configuration Enhanced Ecommerce (pour suivi avancé)
function trackUserEngagement() {
    // Suivi du type d'appareil
    const isMobile = window.innerWidth <= 768;
    const deviceType = isMobile ? 'mobile' : 'desktop';
    
    gtag('event', 'type_appareil', {
        event_category: 'device',
        event_label: deviceType,
        value: 1
    });
    
    // Suivi de la source de trafic (si disponible)
    const referrer = document.referrer;
    if (referrer) {
        let source = 'direct';
        if (referrer.includes('google')) source = 'google';
        else if (referrer.includes('facebook')) source = 'facebook';
        else if (referrer.includes('linkedin')) source = 'linkedin';
        
        gtag('event', 'source_trafic', {
            event_category: 'acquisition',
            event_label: source,
            value: 1
        });
    }
}

// Initialiser le suivi d'engagement
document.addEventListener('DOMContentLoaded', trackUserEngagement);

// Instructions d'installation:
// 1. Remplacer 'GA_MEASUREMENT_ID' dans index.html par votre vrai ID
// 2. Ajouter ce script après le script Google Analytics principal
// 3. Tester les événements dans Google Analytics > Temps réel > Événements
