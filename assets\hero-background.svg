<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <rect width="1920" height="1080" fill="url(#heroGradient)"/>
  
  <!-- Animated pattern -->
  <pattern id="legalPattern" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
    <circle cx="100" cy="100" r="3" fill="#D4AF37" opacity="0.1">
      <animate attributeName="opacity" values="0.1;0.3;0.1" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="50" cy="50" r="2" fill="#D4AF37" opacity="0.05">
      <animate attributeName="opacity" values="0.05;0.2;0.05" dur="6s" repeatCount="indefinite"/>
    </circle>
    <circle cx="150" cy="150" r="2" fill="#D4AF37" opacity="0.05">
      <animate attributeName="opacity" values="0.05;0.2;0.05" dur="5s" repeatCount="indefinite"/>
    </circle>
  </pattern>
  <rect width="1920" height="1080" fill="url(#legalPattern)"/>
  
  <!-- Office elements -->
  <!-- Desk -->
  <rect x="200" y="700" width="800" height="300" rx="20" fill="#8B4513" opacity="0.3"/>
  
  <!-- Books -->
  <rect x="250" y="650" width="60" height="80" rx="5" fill="#2C1810" opacity="0.4"/>
  <rect x="320" y="640" width="60" height="90" rx="5" fill="#2C1810" opacity="0.4"/>
  <rect x="390" y="655" width="60" height="75" rx="5" fill="#2C1810" opacity="0.4"/>
  
  <!-- Documents -->
  <rect x="500" y="680" width="200" height="150" rx="10" fill="#FEFCF8" opacity="0.6"/>
  <rect x="520" y="700" width="160" height="3" rx="1" fill="#2C1810" opacity="0.3"/>
  <rect x="520" y="720" width="140" height="3" rx="1" fill="#2C1810" opacity="0.3"/>
  <rect x="520" y="740" width="160" height="3" rx="1" fill="#2C1810" opacity="0.3"/>
  <rect x="520" y="760" width="120" height="3" rx="1" fill="#2C1810" opacity="0.3"/>
  
  <!-- Stamp -->
  <circle cx="650" cy="780" r="25" fill="#D4AF37" opacity="0.4"/>
  <circle cx="650" cy="780" r="20" stroke="#2C1810" stroke-width="2" fill="none" opacity="0.6"/>
  <text x="650" y="785" text-anchor="middle" fill="#2C1810" font-family="serif" font-size="12" opacity="0.8">CK</text>
  
  <!-- Scales of justice -->
  <g transform="translate(1400, 300)" opacity="0.2">
    <line x1="0" y1="0" x2="0" y2="200" stroke="#D4AF37" stroke-width="8"/>
    <line x1="-80" y1="50" x2="80" y2="50" stroke="#D4AF37" stroke-width="4"/>
    <circle cx="-60" cy="70" r="20" fill="none" stroke="#D4AF37" stroke-width="3"/>
    <circle cx="60" cy="70" r="20" fill="none" stroke="#D4AF37" stroke-width="3"/>
  </g>
  
  <!-- Floating legal symbols -->
  <g opacity="0.1">
    <text x="300" y="200" fill="#D4AF37" font-size="48">⚖️</text>
    <text x="800" y="150" fill="#D4AF37" font-size="36">🏛️</text>
    <text x="1200" y="250" fill="#D4AF37" font-size="42">📋</text>
    <text x="1500" y="180" fill="#D4AF37" font-size="38">🖋️</text>
    <text x="400" y="400" fill="#D4AF37" font-size="32">📜</text>
    <text x="1100" y="450" fill="#D4AF37" font-size="40">🔏</text>
  </g>
  
  <!-- Animated particles -->
  <g opacity="0.3">
    <circle cx="100" cy="100" r="2" fill="#D4AF37">
      <animateTransform attributeName="transform" type="translate" values="0,0; 50,30; 0,0" dur="8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1800" cy="200" r="3" fill="#D4AF37">
      <animateTransform attributeName="transform" type="translate" values="0,0; -30,50; 0,0" dur="10s" repeatCount="indefinite"/>
    </circle>
    <circle cx="500" cy="900" r="2" fill="#D4AF37">
      <animateTransform attributeName="transform" type="translate" values="0,0; 40,-20; 0,0" dur="12s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2C1810;stop-opacity:0.8" />
      <stop offset="30%" style="stop-color:#8B4513;stop-opacity:0.6" />
      <stop offset="70%" style="stop-color:#D4AF37;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#F5E6D3;stop-opacity:0.3" />
    </linearGradient>
  </defs>
</svg>
