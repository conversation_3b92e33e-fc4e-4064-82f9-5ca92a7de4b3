# 🏛️ Site Web Professionnel - Étude Notariale Me Chayma <PERSON>hlifi

[![Netlify Status](https://api.netlify.com/api/v1/badges/YOUR-BADGE-ID/deploy-status)](https://app.netlify.com/sites/YOUR-SITE-NAME/deploys)
[![GitHub Pages](https://img.shields.io/badge/GitHub%20Pages-Live-brightgreen)](https://YOUR-USERNAME.github.io/chaymakhlifi-notaire)

## 🌐 Site en Ligne

**🔗 URL de Production :** [https://chaymakhlifi-notaire.netlify.app](https://chaymakhlifi-notaire.netlify.app)

## 📋 Description

Site web professionnel et élégant pour l'Étude Notariale Me Chayma Khlifi, conçu pour mettre en valeur les services notariaux avec un design moderne et responsive.

**Développé avec les technologies web modernes et déployé automatiquement via GitHub + Netlify.**

## ✨ Fonctionnalités

### Design & UX

- **Design élégant** avec palette beige/doré/blanc/noir
- **Logo professionnel** avec initiales CK
- **Animations modernes** (scroll animé, fade-in, transitions douces)
- **Site responsive** (mobile et desktop)
- **Vidéo de fond** subtile dans la section hero

### Sections Principales

- **Hero Section** avec appels à l'action
- **Services** : Contrats de mariage, Vente immobilière, Succession, Conseil juridique
- **À Propos** avec présentation professionnelle
- **Témoignages clients** avec carousel Swiper
- **FAQ** avec accordéon interactif
- **Contact** avec formulaire et carte Google Maps
- **Footer** complet avec liens et informations

### Fonctionnalités Techniques

- **SEO optimisé** avec métadonnées complètes
- **Formulaire de contact** avec validation
- **Bouton WhatsApp** flottant
- **Bouton scroll-to-top**
- **Navigation smooth scroll**
- **Animations au scroll**
- **Système de notifications**

## 📁 Structure du Projet

```
chaymakhlifi/
├── index.html              # Page principale
├── styles.css              # Styles CSS
├── script.js               # JavaScript
├── mentions-legales.html   # Page mentions légales
├── sitemap.xml            # Plan du site
├── robots.txt             # Instructions pour robots
├── assets/                # Ressources
│   ├── logo-ck.svg        # Logo principal
│   ├── favicon.svg        # Favicon
│   └── [images à ajouter] # Images du site
└── README.md              # Ce fichier
```

## 🖼️ Images à Ajouter

Pour finaliser le site, ajoutez ces images dans le dossier `assets/` :

### Images Obligatoires

1. **hero-video.mp4** - Vidéo de fond pour la section hero (bureau notarial, documents)
2. **notaire-portrait.jpg** - Photo professionnelle de Me Chayma Khlifi
3. **client1.jpg** - Photo du premier client témoignage
4. **client2.jpg** - Photo du deuxième client témoignage
5. **client3.jpg** - Photo du troisième client témoignage
6. **og-image.jpg** - Image pour partage sur réseaux sociaux (1200x630px)
7. **apple-touch-icon.png** - Icône Apple (180x180px)
8. **favicon.ico** - Favicon au format ICO

### Suggestions d'Images

- **Bureau notarial** : Photos du bureau, salle de réunion
- **Documents juridiques** : Contrats, actes notariés (floutés)
- **Équipe** : Photos professionnelles si applicable
- **Bâtiment** : Façade de l'étude notariale

## 🚀 Déploiement Automatique

### GitHub + Netlify (Configuration Actuelle)

Ce projet est configuré pour un déploiement automatique :

1. **Push vers GitHub** → Déploiement automatique sur Netlify
2. **Branches :** `main` pour production, `develop` pour tests
3. **URL de production :** https://chaymakhlifi-notaire.netlify.app
4. **HTTPS automatique** et CDN mondial

### Configuration Netlify

```toml
[build]
  publish = "."

[[redirects]]
  from = "/mentions-legales"
  to = "/mentions-legales.html"
  status = 200

[[redirects]]
  from = "/*"
  to = "/404.html"
  status = 404
```

### Déploiement Manuel (Alternative)

1. Téléchargez le code source
2. Glissez-déposez sur [Netlify](https://netlify.com)
3. Configurez le domaine personnalisé

## 🔧 Configuration

### Google Analytics

1. Remplacez `GA_MEASUREMENT_ID` dans `index.html` par votre ID Google Analytics
2. Configurez le suivi des événements si nécessaire

### Coordonnées

Mettez à jour les informations suivantes dans les fichiers :

- **Adresse** : Remplacez "123 Avenue Habib Bourguiba, 1000 Tunis"
- **Téléphone** : Remplacez "+216 XX XXX XXX"
- **WhatsApp** : Mettez à jour le lien WhatsApp avec le vrai numéro

### Google Maps

1. Obtenez une clé API Google Maps
2. Remplacez l'URL de l'iframe dans la section contact
3. Configurez les coordonnées exactes du bureau

## 📱 Réseaux Sociaux

Mettez à jour les liens vers les réseaux sociaux :

- **Facebook** : Remplacez `#` par l'URL de la page Facebook
- **LinkedIn** : Remplacez `#` par l'URL du profil LinkedIn
- **Instagram** : Remplacez `#` par l'URL du compte Instagram

## 🔍 SEO & Indexation

### Indexation Google

1. Créez un compte [Google Search Console](https://search.google.com/search-console)
2. Ajoutez votre site web
3. Soumettez le sitemap.xml
4. Vérifiez l'indexation des pages

### Mots-clés Optimisés

Le site est optimisé pour :

- "notaire Tunisie"
- "notaire Chayma Khlifi"
- "bureau notarial Tunis"
- "services notariaux Tunisie"

## 🛠️ Maintenance

### Mises à jour Régulières

- Témoignages clients
- Actualités juridiques
- Photos et contenus
- Coordonnées et horaires

### Performance

- Optimisez les images (WebP recommandé)
- Minifiez CSS/JS pour la production
- Configurez la mise en cache

## 📞 Support

Pour toute question technique ou modification :

- Email : [<EMAIL>]
- Documentation : Ce README.md

## 📄 Licence

© 2024 Étude Notariale Me Chayma Khlifi. Tous droits réservés.

---

**Note** : Ce site a été conçu avec les meilleures pratiques web modernes, incluant l'accessibilité, le SEO et la performance. Il est prêt pour la production après ajout des images et configuration des coordonnées.
