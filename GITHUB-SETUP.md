# 📚 Guide de Configuration GitHub

## 🎯 Étape 1 : Créer le Repository GitHub

### 1. Aller sur GitHub
1. Ouvrez votre navigateur et allez sur **https://github.com**
2. Connectez-vous à votre compte (ou créez-en un si nécessaire)

### 2. Créer un nouveau repository
1. Cliquez sur le bouton **"New"** (vert) ou **"+"** en haut à droite
2. Sélectionnez **"New repository"**

### 3. Configuration du repository
Remplissez les informations suivantes :

- **Repository name :** `chaymakhlifi-notaire`
- **Description :** `Site web professionnel pour l'Étude Notariale Me Chayma Khlifi - Design moderne et responsive`
- **Visibilité :** ✅ **Public** (pour Netlify gratuit)
- **Initialize repository :** ❌ **NE PAS cocher** (nous avons déjà du code)
  - ❌ Add a README file
  - ❌ Add .gitignore
  - ❌ Choose a license

4. Cliquez sur **"Create repository"**

## 🔗 Étape 2 : Connecter le Repository Local

Une fois le repository créé, GitHub vous donnera des instructions. Utilisez la section **"push an existing repository from the command line"** :

### Commandes à exécuter dans PowerShell

```bash
# Ajouter l'origine GitHub (remplacez YOUR-USERNAME par votre nom d'utilisateur GitHub)
git remote add origin https://github.com/YOUR-USERNAME/chaymakhlifi-notaire.git

# Pousser le code vers GitHub
git push -u origin main
```

### Exemple avec un nom d'utilisateur fictif
```bash
git remote add origin https://github.com/chaymakhlifi/chaymakhlifi-notaire.git
git push -u origin main
```

## ✅ Vérification

Après avoir exécuté ces commandes :

1. **Rafraîchissez la page GitHub** - vous devriez voir tous vos fichiers
2. **Vérifiez que le README.md s'affiche** correctement
3. **Comptez les fichiers** - vous devriez avoir environ 30 fichiers

## 🚀 Étape 3 : Préparer pour Netlify

Une fois le code sur GitHub, vous êtes prêt pour connecter Netlify !

### Structure attendue sur GitHub :
```
chaymakhlifi-notaire/
├── 📄 index.html (page principale)
├── 🎨 styles.css
├── ⚡ script.js
├── 📁 assets/ (9 images SVG)
├── 🔧 netlify.toml (configuration Netlify)
├── 📋 _redirects
├── 🗺️ sitemap.xml
├── 🤖 robots.txt
└── 📚 Documentation (README.md, guides...)
```

## 🔧 Commandes Git Utiles

### Vérifier le statut
```bash
git status
```

### Voir l'historique
```bash
git log --oneline
```

### Vérifier les remotes
```bash
git remote -v
```

### Pousser des modifications futures
```bash
git add .
git commit -m "Description des modifications"
git push
```

## 🆘 Résolution de Problèmes

### Erreur d'authentification
Si vous avez une erreur d'authentification :
1. Utilisez un **Personal Access Token** au lieu du mot de passe
2. Allez dans GitHub Settings > Developer settings > Personal access tokens
3. Créez un token avec les permissions "repo"

### Repository déjà existant
Si le repository existe déjà :
```bash
git remote set-url origin https://github.com/YOUR-USERNAME/chaymakhlifi-notaire.git
```

## 📝 Notes Importantes

- **Nom du repository :** Utilisez exactement `chaymakhlifi-notaire` pour la cohérence
- **Branche principale :** Nous utilisons `main` (pas `master`)
- **Visibilité :** Public pour utiliser Netlify gratuitement
- **Fichiers sensibles :** Aucun mot de passe ou clé API dans le code

---

**Prochaine étape :** Une fois le code sur GitHub, nous configurerons Netlify pour le déploiement automatique ! 🚀
