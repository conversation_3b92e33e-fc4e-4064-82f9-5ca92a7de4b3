<svg width="400" height="500" viewBox="0 0 400 500" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="500" fill="url(#gradient1)"/>
  
  <!-- Professional silhouette -->
  <ellipse cx="200" cy="180" rx="80" ry="90" fill="#2C1810" opacity="0.8"/>
  
  <!-- Body/Suit -->
  <rect x="120" y="250" width="160" height="250" rx="20" fill="#2C1810" opacity="0.8"/>
  
  <!-- Professional elements -->
  <rect x="50" y="50" width="300" height="20" rx="10" fill="#D4AF37" opacity="0.3"/>
  <rect x="50" y="430" width="300" height="20" rx="10" fill="#D4AF37" opacity="0.3"/>
  
  <!-- Decorative legal symbols -->
  <circle cx="80" cy="120" r="15" fill="#D4AF37" opacity="0.5"/>
  <circle cx="320" cy="120" r="15" fill="#D4AF37" opacity="0.5"/>
  <circle cx="80" cy="380" r="15" fill="#D4AF37" opacity="0.5"/>
  <circle cx="320" cy="380" r="15" fill="#D4AF37" opacity="0.5"/>
  
  <!-- Text placeholder -->
  <text x="200" y="460" text-anchor="middle" fill="#D4AF37" font-family="serif" font-size="16" font-weight="bold">
    Me Chayma Khlifi
  </text>
  <text x="200" y="480" text-anchor="middle" fill="#8B4513" font-family="sans-serif" font-size="12">
    Notaire Professionnel
  </text>
  
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F5E6D3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FEFCF8;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
