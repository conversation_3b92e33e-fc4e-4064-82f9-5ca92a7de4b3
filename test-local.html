<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Local - Site Notaire</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #FEFCF8;
            color: #2C1810;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, #D4AF37, #B8941F);
            color: white;
            border-radius: 1rem;
        }
        .test-item {
            background: white;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-radius: 0.5rem;
            border-left: 4px solid #D4AF37;
            box-shadow: 0 2px 8px rgba(212, 175, 55, 0.1);
        }
        .test-item h3 {
            margin: 0 0 0.5rem 0;
            color: #D4AF37;
        }
        .status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status.success { background: #4CAF50; color: white; }
        .status.warning { background: #FF9800; color: white; }
        .status.error { background: #f44336; color: white; }
        .button {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #D4AF37;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: #B8941F;
            transform: translateY(-2px);
        }
        .checklist {
            background: #F5E6D3;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin: 2rem 0;
        }
        .checklist h3 {
            margin-top: 0;
            color: #2C1810;
        }
        .checklist ul {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(212, 175, 55, 0.2);
        }
        .checklist li:last-child {
            border-bottom: none;
        }
        .code {
            background: #2C1810;
            color: #D4AF37;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏛️ Test Local - Site Notaire Chayma Khlifi</h1>
        <p>Vérifiez que tout fonctionne correctement avant le déploiement</p>
    </div>

    <div class="test-item">
        <h3>📁 Structure des Fichiers</h3>
        <p>Vérifiez que tous les fichiers sont présents :</p>
        <div class="checklist">
            <ul>
                <li>✅ index.html - Page principale</li>
                <li>✅ styles.css - Feuille de style</li>
                <li>✅ script.js - JavaScript</li>
                <li>✅ assets/logo-ck.svg - Logo</li>
                <li>✅ assets/favicon.svg - Favicon</li>
                <li>⚠️ assets/hero-video.mp4 - Vidéo de fond (à ajouter)</li>
                <li>⚠️ assets/notaire-portrait.jpg - Photo notaire (à ajouter)</li>
                <li>⚠️ assets/client1.jpg - Photo client 1 (à ajouter)</li>
                <li>⚠️ assets/client2.jpg - Photo client 2 (à ajouter)</li>
                <li>⚠️ assets/client3.jpg - Photo client 3 (à ajouter)</li>
            </ul>
        </div>
    </div>

    <div class="test-item">
        <h3>🌐 Pages du Site</h3>
        <p>Testez toutes les pages :</p>
        <a href="index.html" class="button">Page Principale</a>
        <a href="mentions-legales.html" class="button">Mentions Légales</a>
        <a href="404.html" class="button">Page 404</a>
        <a href="thank-you.html" class="button">Page Remerciement</a>
    </div>

    <div class="test-item">
        <h3>📱 Test Responsive</h3>
        <p>Testez le site sur différentes tailles d'écran :</p>
        <div class="code">
            F12 → Toggle Device Toolbar → Testez :
            - Mobile (375px)
            - Tablet (768px) 
            - Desktop (1200px)
        </div>
    </div>

    <div class="test-item">
        <h3>⚙️ Fonctionnalités à Tester</h3>
        <div class="checklist">
            <ul>
                <li>🔗 Navigation smooth scroll</li>
                <li>📱 Menu mobile (hamburger)</li>
                <li>📝 Formulaire de contact</li>
                <li>🎠 Carousel témoignages</li>
                <li>❓ Accordéon FAQ</li>
                <li>⬆️ Bouton scroll-to-top</li>
                <li>💬 Bouton WhatsApp</li>
                <li>🎨 Animations au scroll</li>
            </ul>
        </div>
    </div>

    <div class="test-item">
        <h3>🔧 Configuration Requise</h3>
        <p>Avant le déploiement, configurez :</p>
        <div class="checklist">
            <h4>Coordonnées :</h4>
            <ul>
                <li>📍 Adresse réelle du bureau</li>
                <li>📞 Numéro de téléphone</li>
                <li>📧 Adresse email</li>
                <li>💬 Numéro WhatsApp</li>
            </ul>
            
            <h4>Services Web :</h4>
            <ul>
                <li>📊 ID Google Analytics</li>
                <li>🗺️ Intégration Google Maps</li>
                <li>📱 Liens réseaux sociaux</li>
                <li>🌐 Nom de domaine</li>
            </ul>
        </div>
    </div>

    <div class="test-item">
        <h3>🚀 Serveur Local</h3>
        <p>Pour tester avec un serveur local :</p>
        <div class="code">
            # Python 3
            python -m http.server 8000
            
            # Node.js (avec live-server)
            npx live-server
            
            # PHP
            php -S localhost:8000
        </div>
        <p>Puis ouvrez : <strong>http://localhost:8000</strong></p>
    </div>

    <div class="test-item">
        <h3>✅ Checklist Final</h3>
        <div class="checklist">
            <ul>
                <li>□ Toutes les images ajoutées</li>
                <li>□ Coordonnées mises à jour</li>
                <li>□ Liens sociaux configurés</li>
                <li>□ Google Analytics configuré</li>
                <li>□ Formulaire testé</li>
                <li>□ Site responsive vérifié</li>
                <li>□ Performance optimisée</li>
                <li>□ SEO vérifié</li>
            </ul>
        </div>
    </div>

    <div class="test-item">
        <h3>📚 Documentation</h3>
        <p>Consultez la documentation complète :</p>
        <a href="README.md" class="button">README.md</a>
        <a href="GUIDE-DEPLOIEMENT.md" class="button">Guide Déploiement</a>
    </div>

    <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: #F5E6D3; border-radius: 1rem;">
        <h3 style="color: #D4AF37; margin-bottom: 1rem;">🎉 Prêt pour le Déploiement ?</h3>
        <p style="margin-bottom: 1.5rem;">Une fois tous les tests validés, suivez le guide de déploiement pour mettre votre site en ligne !</p>
        <a href="GUIDE-DEPLOIEMENT.md" class="button" style="font-size: 1.1rem; padding: 1rem 2rem;">
            🚀 Déployer le Site
        </a>
    </div>

    <script>
        // Test JavaScript
        console.log('✅ JavaScript fonctionne correctement');
        
        // Test des fonctionnalités
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ DOM chargé');
            
            // Vérifier si les fichiers CSS sont chargés
            const styles = window.getComputedStyle(document.body);
            if (styles.fontFamily.includes('Inter')) {
                console.log('✅ Fonts Google chargées');
            } else {
                console.warn('⚠️ Fonts Google non chargées');
            }
        });
    </script>
</body>
</html>
