<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="60" cy="60" r="60" fill="url(#clientGradient3)"/>
  
  <!-- Face silhouette -->
  <circle cx="60" cy="45" r="22" fill="#2C1810" opacity="0.7"/>
  
  <!-- Body -->
  <ellipse cx="60" cy="85" rx="28" ry="35" fill="#2C1810" opacity="0.7"/>
  
  <!-- Suit/tie suggestion -->
  <rect x="57" y="70" width="6" height="20" fill="#D4AF37" opacity="0.6"/>
  
  <!-- Decorative elements -->
  <circle cx="60" cy="60" r="55" stroke="#D4AF37" stroke-width="2" fill="none" opacity="0.5"/>
  
  <defs>
    <linearGradient id="clientGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E8DCC6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:0.2" />
    </linearGradient>
  </defs>
</svg>
