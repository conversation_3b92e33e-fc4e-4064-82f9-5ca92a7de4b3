<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#ogGradient)"/>
  
  <!-- Decorative pattern -->
  <pattern id="pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
    <circle cx="50" cy="50" r="2" fill="#D4AF37" opacity="0.1"/>
  </pattern>
  <rect width="1200" height="630" fill="url(#pattern)"/>
  
  <!-- Main content area -->
  <rect x="100" y="100" width="1000" height="430" rx="20" fill="rgba(255,255,255,0.95)" stroke="#D4AF37" stroke-width="3"/>
  
  <!-- Logo -->
  <circle cx="250" cy="250" r="60" fill="#D4AF37"/>
  <circle cx="250" cy="250" r="45" fill="#F5E6D3" opacity="0.3"/>
  
  <!-- CK Letters in logo -->
  <text x="230" y="270" fill="#2C1810" font-family="serif" font-size="36" font-weight="bold">CK</text>
  
  <!-- Main title -->
  <text x="350" y="220" fill="#2C1810" font-family="serif" font-size="48" font-weight="bold">
    Étude Notariale
  </text>
  <text x="350" y="280" fill="#D4AF37" font-family="serif" font-size="56" font-weight="bold">
    Me Chayma Khlifi
  </text>
  
  <!-- Subtitle -->
  <text x="350" y="320" fill="#8B4513" font-family="sans-serif" font-size="24" font-weight="500">
    Confiance • Transparence • Expertise
  </text>
  
  <!-- Services -->
  <text x="350" y="370" fill="#5A4A3A" font-family="sans-serif" font-size="20">
    ✓ Contrats de mariage  ✓ Vente immobilière
  </text>
  <text x="350" y="400" fill="#5A4A3A" font-family="sans-serif" font-size="20">
    ✓ Succession et héritage  ✓ Conseil juridique
  </text>
  
  <!-- Contact info -->
  <text x="350" y="450" fill="#D4AF37" font-family="sans-serif" font-size="18" font-weight="600">
    📍 Tunis, Tunisie  📞 +216 XX XXX XXX
  </text>
  <text x="350" y="480" fill="#D4AF37" font-family="sans-serif" font-size="18" font-weight="600">
    ✉️ <EMAIL>
  </text>
  
  <!-- Decorative elements -->
  <rect x="900" y="150" width="200" height="300" rx="10" fill="#F5E6D3" opacity="0.5"/>
  <circle cx="1000" cy="200" r="30" fill="#D4AF37" opacity="0.3"/>
  <circle cx="1000" cy="300" r="25" fill="#D4AF37" opacity="0.3"/>
  <circle cx="1000" cy="400" r="20" fill="#D4AF37" opacity="0.3"/>
  
  <!-- Legal symbols -->
  <text x="970" y="220" fill="#2C1810" font-family="serif" font-size="24">⚖️</text>
  <text x="970" y="320" fill="#2C1810" font-family="serif" font-size="24">🏛️</text>
  <text x="970" y="420" fill="#2C1810" font-family="serif" font-size="24">📋</text>
  
  <defs>
    <linearGradient id="ogGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEFCF8;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F5E6D3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E8DCC6;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
