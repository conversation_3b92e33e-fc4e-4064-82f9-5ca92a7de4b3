# Script de deploiement PowerShell pour Netlify
# Usage: .\deploy.ps1

Write-Host "Deploiement Site Notaire Chayma <PERSON>hlifi" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Vérifier que tous les fichiers sont présents
$requiredFiles = @(
    "index.html",
    "styles.css", 
    "script.js",
    "sitemap.xml",
    "robots.txt",
    "manifest.json",
    "_redirects",
    "netlify.toml"
)

Write-Host "Verification des fichiers..." -ForegroundColor Yellow

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "OK $file" -ForegroundColor Green
    }
    else {
        Write-Host "ERREUR $file manquant!" -ForegroundColor Red
        exit 1
    }
}

# Verifier le dossier assets
if (Test-Path "assets") {
    $assetFiles = Get-ChildItem "assets" -Name
    Write-Host "Assets trouves: $($assetFiles.Count) fichiers" -ForegroundColor Green
    foreach ($asset in $assetFiles) {
        Write-Host "   - $asset" -ForegroundColor Cyan
    }
}
else {
    Write-Host "ERREUR Dossier assets manquant!" -ForegroundColor Red
    exit 1
}

# Creer l'archive de deploiement
Write-Host "Creation de l'archive de deploiement..." -ForegroundColor Yellow

$deployFiles = @(
    "index.html",
    "styles.css",
    "script.js", 
    "mentions-legales.html",
    "404.html",
    "thank-you.html",
    "sitemap.xml",
    "robots.txt",
    "manifest.json",
    "_redirects",
    "netlify.toml",
    "assets"
)

$zipPath = "chaymakhlifi-notaire-deploy.zip"

# Supprimer l'ancienne archive si elle existe
if (Test-Path $zipPath) {
    Remove-Item $zipPath
}

# Creer la nouvelle archive
Compress-Archive -Path $deployFiles -DestinationPath $zipPath -Force

Write-Host "OK Archive creee: $zipPath" -ForegroundColor Green

# Afficher les instructions de deploiement
Write-Host ""
Write-Host "Instructions de deploiement Netlify:" -ForegroundColor Cyan
Write-Host "1. Aller sur https://netlify.com" -ForegroundColor White
Write-Host "2. Se connecter ou creer un compte" -ForegroundColor White
Write-Host "3. Cliquer 'Add new site' > 'Deploy manually'" -ForegroundColor White
Write-Host "4. Glisser-deposer le fichier: $zipPath" -ForegroundColor Yellow
Write-Host "5. Attendre le deploiement (2-3 minutes)" -ForegroundColor White
Write-Host ""
Write-Host "Apres deploiement:" -ForegroundColor Cyan
Write-Host "- Configurer le domaine personnalise" -ForegroundColor White
Write-Host "- Activer HTTPS" -ForegroundColor White
Write-Host "- Tester toutes les pages" -ForegroundColor White
Write-Host ""
Write-Host "Site pret pour la production!" -ForegroundColor Green
