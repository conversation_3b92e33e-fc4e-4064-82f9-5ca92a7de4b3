/* ===== GOOGLE FONTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap');

/* ===== VARIABLES CSS ===== */
:root {
  --header-height: 3.5rem;

  /* ========== Colors ========== */
  --primary-color: #D4AF37; /* Gold */
  --primary-color-alt: #B8941F;
  --secondary-color: #F5E6D3; /* Beige */
  --accent-color: #8B4513; /* Brown */
  --title-color: #2C1810;
  --text-color: #5A4A3A;
  --text-color-light: #8B7355;
  --body-color: #FEFCF8;
  --container-color: #FFFFFF;
  --border-color: #E8DCC6;
  --shadow-color: rgba(212, 175, 55, 0.1);
  --overlay-color: rgba(44, 24, 16, 0.7);

  /* ========== Font and typography ========== */
  --body-font: 'Inter', sans-serif;
  --title-font: 'Playfair Display', serif;
  --biggest-font-size: 3rem;
  --h1-font-size: 2.25rem;
  --h2-font-size: 1.75rem;
  --h3-font-size: 1.25rem;
  --normal-font-size: 1rem;
  --small-font-size: 0.875rem;
  --smaller-font-size: 0.813rem;

  /* ========== Font weight ========== */
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semi-bold: 600;
  --font-bold: 700;

  /* ========== z index ========== */
  --z-tooltip: 10;
  --z-fixed: 100;
  --z-modal: 1000;

  /* ========== Margins Bottom ========== */
  --mb-0-25: 0.25rem;
  --mb-0-5: 0.5rem;
  --mb-0-75: 0.75rem;
  --mb-1: 1rem;
  --mb-1-5: 1.5rem;
  --mb-2: 2rem;
  --mb-2-5: 2.5rem;
  --mb-3: 3rem;

  /* ========== Transitions ========== */
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* Responsive typography */
@media screen and (max-width: 992px) {
  :root {
    --biggest-font-size: 2.25rem;
    --h1-font-size: 1.75rem;
    --h2-font-size: 1.5rem;
    --h3-font-size: 1.125rem;
    --normal-font-size: 0.938rem;
    --small-font-size: 0.813rem;
    --smaller-font-size: 0.75rem;
  }
}

/* ===== BASE ===== */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body,
button,
input,
textarea {
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
}

body {
  background-color: var(--body-color);
  color: var(--text-color);
  line-height: 1.6;
}

h1, h2, h3, h4 {
  color: var(--title-color);
  font-weight: var(--font-semi-bold);
  font-family: var(--title-font);
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
}

/* ===== REUSABLE CSS CLASSES ===== */
.container {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.grid {
  display: grid;
}

.section {
  padding: 6rem 0 2rem;
}

.section__header {
  text-align: center;
  margin-bottom: var(--mb-3);
}

.section__subtitle {
  display: block;
  font-size: var(--small-font-size);
  color: var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: var(--font-medium);
  margin-bottom: var(--mb-0-5);
}

.section__title {
  font-size: var(--h2-font-size);
  margin-bottom: var(--mb-1);
  font-family: var(--title-font);
  font-weight: var(--font-bold);
}

.section__description {
  color: var(--text-color-light);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== BUTTONS ===== */
.button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--primary-color);
  color: var(--container-color);
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-weight: var(--font-medium);
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: var(--small-font-size);
}

.button:hover {
  background-color: var(--primary-color-alt);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px var(--shadow-color);
}

.button--outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.button--outline:hover {
  background-color: var(--primary-color);
  color: var(--container-color);
}

.button i {
  font-size: 1.25rem;
}

/* ===== HEADER & NAV ===== */
.header {
  width: 100%;
  background-color: var(--container-color);
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-fixed);
  transition: var(--transition);
  box-shadow: 0 2px 16px var(--shadow-color);
}

.nav {
  height: var(--header-height);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav__logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--title-color);
  font-weight: var(--font-semi-bold);
  font-family: var(--title-font);
}

.nav__logo-img {
  width: 40px;
  height: 40px;
}

.nav__list {
  display: flex;
  gap: 2rem;
}

.nav__link {
  color: var(--text-color);
  font-weight: var(--font-medium);
  transition: var(--transition);
  position: relative;
}

.nav__link:hover,
.nav__link.active-link {
  color: var(--primary-color);
}

.nav__link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -0.5rem;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.nav__link:hover::after,
.nav__link.active-link::after {
  width: 100%;
}

.nav__toggle,
.nav__close {
  display: none;
}

/* ===== HERO ===== */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.hero__video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--overlay-color);
  z-index: -1;
}

.hero__container {
  position: relative;
  z-index: 1;
}

.hero__data {
  text-align: center;
  color: var(--container-color);
}

.hero__title {
  font-size: var(--biggest-font-size);
  font-family: var(--title-font);
  font-weight: var(--font-bold);
  margin-bottom: var(--mb-1);
  line-height: 1.2;
}

.hero__title-accent {
  color: var(--primary-color);
}

.hero__subtitle {
  font-size: var(--h3-font-size);
  color: var(--primary-color);
  margin-bottom: var(--mb-1);
  font-weight: var(--font-medium);
  letter-spacing: 2px;
}

.hero__description {
  font-size: var(--normal-font-size);
  margin-bottom: var(--mb-2-5);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
}

.hero__buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero__scroll {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
}

.hero__scroll-link {
  color: var(--container-color);
  font-size: 1.5rem;
  animation: scroll 2s ease-in-out infinite;
}

@keyframes scroll {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* ===== SERVICES ===== */
.services {
  background-color: var(--secondary-color);
}

.services__container {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.services__card {
  background-color: var(--container-color);
  padding: 2.5rem 2rem;
  border-radius: 1rem;
  text-align: center;
  transition: var(--transition);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.services__card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: var(--transition-slow);
}

.services__card:hover::before {
  left: 100%;
}

.services__card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px var(--shadow-color);
}

.services__icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-color-alt));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--mb-1-5);
  transition: var(--transition);
}

.services__icon i {
  font-size: 2rem;
  color: var(--container-color);
}

.services__card:hover .services__icon {
  transform: rotateY(360deg);
}

.services__title {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-1);
  font-family: var(--title-font);
}

.services__description {
  color: var(--text-color-light);
  margin-bottom: var(--mb-1-5);
  line-height: 1.6;
}

.services__link {
  color: var(--primary-color);
  font-weight: var(--font-medium);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.services__link:hover {
  gap: 1rem;
}

/* ===== ABOUT ===== */
.about__container {
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about__data {
  text-align: left;
}

.about__data .section__header {
  text-align: left;
  margin-bottom: var(--mb-2);
}

.about__description {
  margin-bottom: var(--mb-2);
  color: var(--text-color-light);
  line-height: 1.8;
}

.about__info {
  margin-bottom: var(--mb-2-5);
}

.about__info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: var(--mb-1-5);
}

.about__info-item i {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-top: 0.25rem;
}

.about__info-item h4 {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-0-25);
  font-family: var(--title-font);
}

.about__info-item p {
  color: var(--text-color-light);
}

.about__img {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
}

.about__img-main {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 1rem;
}

.about__img-overlay {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

.about__stats {
  display: flex;
  justify-content: space-around;
}

.about__stat {
  text-align: center;
}

.about__stat-number {
  display: block;
  font-size: var(--h2-font-size);
  font-weight: var(--font-bold);
  color: var(--primary-color);
  font-family: var(--title-font);
}

.about__stat-text {
  font-size: var(--small-font-size);
  color: var(--text-color);
  font-weight: var(--font-medium);
}

/* ===== TESTIMONIALS ===== */
.testimonials {
  background-color: var(--secondary-color);
}

.testimonials__container {
  max-width: 800px;
  margin: 0 auto;
}

.testimonials__card {
  background-color: var(--container-color);
  padding: 3rem 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 8px 32px var(--shadow-color);
  margin: 1rem;
}

.testimonials__quote {
  margin-bottom: var(--mb-1-5);
}

.testimonials__quote i {
  font-size: 2rem;
  color: var(--primary-color);
}

.testimonials__description {
  font-style: italic;
  margin-bottom: var(--mb-2);
  color: var(--text-color-light);
  line-height: 1.8;
  font-size: var(--normal-font-size);
}

.testimonials__author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.testimonials__img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.testimonials__data {
  text-align: left;
}

.testimonials__name {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-0-25);
  font-family: var(--title-font);
}

.testimonials__job {
  font-size: var(--small-font-size);
  color: var(--primary-color);
  font-weight: var(--font-medium);
}

/* Swiper pagination */
.swiper-pagination-bullet {
  background-color: var(--primary-color);
  opacity: 0.3;
}

.swiper-pagination-bullet-active {
  opacity: 1;
}

/* ===== FAQ ===== */
.faq__container {
  max-width: 800px;
  margin: 0 auto;
}

.faq__item {
  background-color: var(--container-color);
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: var(--transition);
}

.faq__item:hover {
  box-shadow: 0 4px 16px var(--shadow-color);
}

.faq__header {
  padding: 1.5rem 2rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: var(--transition);
}

.faq__header:hover {
  background-color: var(--secondary-color);
}

.faq__question {
  font-size: var(--h3-font-size);
  font-family: var(--title-font);
  color: var(--title-color);
}

.faq__icon {
  font-size: 1.25rem;
  color: var(--primary-color);
  transition: var(--transition);
}

.faq__content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq__item.active .faq__content {
  max-height: 200px;
}

.faq__item.active .faq__icon {
  transform: rotate(180deg);
}

.faq__answer {
  padding: 0 2rem 1.5rem;
  color: var(--text-color-light);
  line-height: 1.6;
}

/* ===== CONTACT ===== */
.contact {
  background-color: var(--secondary-color);
}

.contact__container {
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: var(--mb-3);
}

.contact__data {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact__info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--container-color);
  border-radius: 0.5rem;
  transition: var(--transition);
}

.contact__info-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px var(--shadow-color);
}

.contact__info-item i {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-top: 0.25rem;
}

.contact__info-item h4 {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-0-25);
  font-family: var(--title-font);
}

.contact__info-item p {
  color: var(--text-color-light);
}

.contact__social {
  background-color: var(--container-color);
  padding: 1.5rem;
  border-radius: 0.5rem;
  text-align: center;
}

.contact__social h4 {
  margin-bottom: var(--mb-1);
  font-family: var(--title-font);
}

.contact__social-links {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.contact__social-link {
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: var(--container-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  transition: var(--transition);
}

.contact__social-link:hover {
  background-color: var(--primary-color-alt);
  transform: translateY(-3px);
}

.contact__form {
  background-color: var(--container-color);
  padding: 2.5rem;
  border-radius: 1rem;
  box-shadow: 0 8px 32px var(--shadow-color);
}

.contact__form-group {
  margin-bottom: var(--mb-1-5);
}

.contact__input {
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--body-color);
  color: var(--text-color);
  font-family: var(--body-font);
  transition: var(--transition);
}

.contact__input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.contact__textarea {
  resize: vertical;
  min-height: 120px;
}

.contact__button {
  width: 100%;
  justify-content: center;
}

.contact__map {
  margin-top: var(--mb-3);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 8px 32px var(--shadow-color);
}

.contact__map iframe {
  border-radius: 1rem;
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--title-color);
  color: var(--container-color);
  padding: 4rem 0 2rem;
}

.footer__container {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: var(--mb-2);
}

.footer__content {
  display: flex;
  flex-direction: column;
}

.footer__logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: var(--mb-1);
}

.footer__logo-img {
  width: 40px;
  height: 40px;
}

.footer__logo-text {
  font-size: var(--h3-font-size);
  font-family: var(--title-font);
  font-weight: var(--font-semi-bold);
}

.footer__description {
  margin-bottom: var(--mb-1-5);
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.footer__social {
  display: flex;
  gap: 1rem;
}

.footer__social-link {
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  color: var(--container-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.footer__social-link:hover {
  background-color: var(--primary-color-alt);
  transform: translateY(-3px);
}

.footer__title {
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-1);
  font-family: var(--title-font);
  color: var(--primary-color);
}

.footer__links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer__link {
  color: rgba(255, 255, 255, 0.8);
  transition: var(--transition);
}

.footer__link:hover {
  color: var(--primary-color);
  padding-left: 0.5rem;
}

.footer__info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer__info li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.footer__info i {
  color: var(--primary-color);
  font-size: 1rem;
}

.footer__bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--mb-1-5);
  text-align: center;
}

.footer__copy {
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--small-font-size);
}

/* ===== FLOATING BUTTONS ===== */
.whatsapp-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  background-color: #25D366;
  color: var(--container-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  z-index: var(--z-tooltip);
  transition: var(--transition);
  animation: pulse 2s infinite;
}

.whatsapp-btn:hover {
  transform: scale(1.1);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
  }
}

.scrollup {
  position: fixed;
  right: 2rem;
  bottom: 6rem;
  background-color: var(--primary-color);
  color: var(--container-color);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-tooltip);
  transition: var(--transition);
  opacity: 0;
  visibility: hidden;
}

.scrollup.show-scroll {
  opacity: 1;
  visibility: visible;
}

.scrollup:hover {
  background-color: var(--primary-color-alt);
  transform: translateY(-3px);
}

/* ===== ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.show {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.6s ease;
}

.slide-in-left.show {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.6s ease;
}

.slide-in-right.show {
  opacity: 1;
  transform: translateX(0);
}

/* ===== MEDIA QUERIES ===== */
/* For large devices */
@media screen and (max-width: 992px) {
  .container {
    margin-left: var(--mb-1-5);
    margin-right: var(--mb-1-5);
  }

  .section {
    padding: 4rem 0 2rem;
  }

  .hero__buttons {
    flex-direction: column;
    align-items: center;
  }

  .services__container {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .about__container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about__img-main {
    height: 400px;
  }

  .contact__container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer__container {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

/* For medium devices */
@media screen and (max-width: 768px) {
  .nav__menu {
    position: fixed;
    top: var(--header-height);
    right: -100%;
    width: 100%;
    height: calc(100vh - var(--header-height));
    background-color: var(--container-color);
    padding: 2rem 1.5rem;
    transition: var(--transition);
    box-shadow: 0 2px 16px var(--shadow-color);
  }

  .nav__menu.show-menu {
    right: 0;
  }

  .nav__list {
    flex-direction: column;
    gap: 1.5rem;
  }

  .nav__link {
    font-size: var(--h3-font-size);
  }

  .nav__close,
  .nav__toggle {
    display: block;
    font-size: 1.5rem;
    color: var(--title-color);
    cursor: pointer;
  }

  .nav__close {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
  }

  .hero__title {
    font-size: 2rem;
  }

  .hero__buttons {
    gap: 0.5rem;
  }

  .button {
    padding: 0.75rem 1.5rem;
    font-size: var(--smaller-font-size);
  }

  .services__card {
    padding: 2rem 1.5rem;
  }

  .testimonials__card {
    padding: 2rem 1.5rem;
    margin: 0.5rem;
  }

  .testimonials__author {
    flex-direction: column;
    gap: 0.5rem;
  }

  .testimonials__data {
    text-align: center;
  }

  .contact__form {
    padding: 2rem 1.5rem;
  }

  .footer__container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .footer__social {
    justify-content: center;
  }

  .whatsapp-btn {
    bottom: 1rem;
    right: 1rem;
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .scrollup {
    right: 1rem;
    bottom: 4rem;
    width: 40px;
    height: 40px;
  }
}

/* For small devices */
@media screen and (max-width: 350px) {
  .container {
    margin-left: var(--mb-1);
    margin-right: var(--mb-1);
  }

  .hero__title {
    font-size: 1.75rem;
  }

  .hero__buttons {
    flex-direction: column;
    width: 100%;
  }

  .button {
    width: 100%;
    justify-content: center;
  }

  .services__container {
    grid-template-columns: 1fr;
  }

  .services__card {
    padding: 1.5rem 1rem;
  }

  .about__img-overlay {
    left: 1rem;
    right: 1rem;
    bottom: 1rem;
    padding: 1rem;
  }

  .about__stats {
    flex-direction: column;
    gap: 1rem;
  }

  .testimonials__card {
    padding: 1.5rem 1rem;
  }

  .contact__form {
    padding: 1.5rem 1rem;
  }

  .contact__info-item {
    padding: 1rem;
  }

  .footer__container {
    gap: 1.5rem;
  }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: var(--mb-1); }
.mb-2 { margin-bottom: var(--mb-2); }
.mb-3 { margin-bottom: var(--mb-3); }

.hidden { display: none; }
.visible { display: block; }

/* ===== LOADING ANIMATION ===== */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--container-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ===== NOTIFICATION ANIMATIONS ===== */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* ===== SCROLL HEADER ===== */
.scroll-header {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 16px rgba(212, 175, 55, 0.15);
}

/* ===== LOADER ===== */
.loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--body-color);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  transition: var(--transition-slow);
}

.loader__spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ===== ENHANCED HOVER EFFECTS ===== */
.services__card,
.testimonials__card,
.contact__info-item {
  position: relative;
  overflow: hidden;
}

.services__card::after,
.testimonials__card::after,
.contact__info-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: var(--transition-slow);
}

.services__card:hover::after,
.testimonials__card:hover::after,
.contact__info-item:hover::after {
  left: 100%;
}

/* ===== FOCUS STYLES FOR ACCESSIBILITY ===== */
.button:focus,
.nav__link:focus,
.contact__input:focus,
.footer__link:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
  .header,
  .whatsapp-btn,
  .scrollup,
  .hero__video,
  .contact__map {
    display: none !important;
  }

  .hero {
    height: auto;
    padding: 2rem 0;
  }

  .hero__overlay {
    display: none;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .section {
    padding: 1rem 0;
    page-break-inside: avoid;
  }
}
