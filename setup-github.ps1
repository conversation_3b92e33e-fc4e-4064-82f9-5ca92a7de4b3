# Script de configuration GitHub pour le site Notaire Chayma Khlifi
# Usage: .\setup-github.ps1

Write-Host "Configuration GitHub - Site Notaire Chayma Khlifi" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Demander le nom d'utilisateur GitHub
Write-Host ""
$githubUsername = Read-Host "Entrez votre nom d'utilisateur GitHub"

if ([string]::IsNullOrWhiteSpace($githubUsername)) {
    Write-Host "ERREUR: Nom d'utilisateur requis!" -ForegroundColor Red
    exit 1
}

# Construire l'URL du repository
$repoUrl = "https://github.com/$githubUsername/chaymakhlifi-notaire.git"

Write-Host ""
Write-Host "Configuration du repository:" -ForegroundColor Yellow
Write-Host "URL: $repoUrl" -ForegroundColor Cyan
Write-Host "Branche: main" -ForegroundColor Cyan

# Vérifier si Git est initialisé
if (-not (Test-Path ".git")) {
    Write-Host "ERREUR: Repository Git non initialisé!" -ForegroundColor Red
    Write-Host "Executez d'abord: git init" -ForegroundColor Yellow
    exit 1
}

# Vérifier s'il y a des commits
try {
    $commits = git log --oneline 2>$null
    if ([string]::IsNullOrWhiteSpace($commits)) {
        Write-Host "ERREUR: Aucun commit trouve!" -ForegroundColor Red
        Write-Host "Executez d'abord: git add . && git commit -m 'Initial commit'" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "ERREUR: Aucun commit trouve!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Verification du repository local..." -ForegroundColor Yellow

# Vérifier les fichiers essentiels
$requiredFiles = @("index.html", "styles.css", "script.js", "netlify.toml", "_redirects")
$missingFiles = @()

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "ERREUR: Fichiers manquants:" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
    exit 1
}

Write-Host "OK Tous les fichiers essentiels sont presents" -ForegroundColor Green

# Ajouter l'origine GitHub
Write-Host ""
Write-Host "Ajout de l'origine GitHub..." -ForegroundColor Yellow

try {
    # Vérifier si origin existe déjà
    $existingOrigin = git remote get-url origin 2>$null
    if ($existingOrigin) {
        Write-Host "Origin existe deja: $existingOrigin" -ForegroundColor Yellow
        $response = Read-Host "Voulez-vous le remplacer? (y/N)"
        if ($response -eq "y" -or $response -eq "Y") {
            git remote set-url origin $repoUrl
            Write-Host "Origin mis a jour" -ForegroundColor Green
        } else {
            Write-Host "Conservation de l'origin existant" -ForegroundColor Yellow
        }
    } else {
        git remote add origin $repoUrl
        Write-Host "Origin ajoute avec succes" -ForegroundColor Green
    }
} catch {
    Write-Host "ERREUR lors de l'ajout de l'origin: $_" -ForegroundColor Red
    exit 1
}

# Pousser vers GitHub
Write-Host ""
Write-Host "Push vers GitHub..." -ForegroundColor Yellow
Write-Host "IMPORTANT: Vous devrez peut-etre entrer vos identifiants GitHub" -ForegroundColor Cyan

try {
    git push -u origin main
    Write-Host ""
    Write-Host "SUCCESS! Code pousse vers GitHub" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "ERREUR lors du push: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "Solutions possibles:" -ForegroundColor Yellow
    Write-Host "1. Verifiez vos identifiants GitHub" -ForegroundColor White
    Write-Host "2. Utilisez un Personal Access Token au lieu du mot de passe" -ForegroundColor White
    Write-Host "3. Assurez-vous que le repository existe sur GitHub" -ForegroundColor White
    Write-Host ""
    Write-Host "Pour creer le repository sur GitHub:" -ForegroundColor Cyan
    Write-Host "1. Allez sur https://github.com" -ForegroundColor White
    Write-Host "2. Cliquez 'New repository'" -ForegroundColor White
    Write-Host "3. Nom: chaymakhlifi-notaire" -ForegroundColor White
    Write-Host "4. Public, sans README/gitignore" -ForegroundColor White
    exit 1
}

# Vérifier le push
Write-Host ""
Write-Host "Verification..." -ForegroundColor Yellow

try {
    $remoteInfo = git remote -v
    Write-Host "Remotes configures:" -ForegroundColor Green
    Write-Host $remoteInfo -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "Derniers commits:" -ForegroundColor Green
    $recentCommits = git log --oneline -3
    Write-Host $recentCommits -ForegroundColor Cyan
} catch {
    Write-Host "Impossible de verifier le statut" -ForegroundColor Yellow
}

# Instructions finales
Write-Host ""
Write-Host "ETAPES SUIVANTES:" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green
Write-Host ""
Write-Host "1. Verifiez votre repository GitHub:" -ForegroundColor Yellow
Write-Host "   https://github.com/$githubUsername/chaymakhlifi-notaire" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Vous devriez voir tous vos fichiers (environ 30)" -ForegroundColor Yellow
Write-Host ""
Write-Host "3. Prochaine etape: Connecter a Netlify" -ForegroundColor Yellow
Write-Host "   - Allez sur https://netlify.com" -ForegroundColor White
Write-Host "   - 'New site from Git' > GitHub" -ForegroundColor White
Write-Host "   - Selectionnez 'chaymakhlifi-notaire'" -ForegroundColor White
Write-Host ""
Write-Host "Site pret pour le deploiement automatique!" -ForegroundColor Green
