[build]
  publish = "."
  
[build.environment]
  NODE_VERSION = "18"

# Redirects and rewrites
[[redirects]]
  from = "/mentions-legales"
  to = "/mentions-legales.html"
  status = 200

[[redirects]]
  from = "/*"
  to = "/404.html"
  status = 404

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "geolocation=(), microphone=(), camera=()"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.svg"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# Form handling (Netlify Forms)
[[forms]]
  name = "contact"
  action = "/thank-you"
  
# Edge functions (optional)
# [[edge_functions]]
#   function = "contact-form"
#   path = "/api/contact"
