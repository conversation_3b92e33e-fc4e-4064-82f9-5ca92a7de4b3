<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="60" cy="60" r="60" fill="url(#clientGradient2)"/>
  
  <!-- Face silhouette -->
  <circle cx="60" cy="45" r="18" fill="#2C1810" opacity="0.7"/>
  
  <!-- Body -->
  <ellipse cx="60" cy="85" rx="22" ry="35" fill="#2C1810" opacity="0.7"/>
  
  <!-- Hair/headscarf suggestion -->
  <path d="M42 35 Q60 25 78 35 Q75 45 60 45 Q45 45 42 35" fill="#2C1810" opacity="0.5"/>
  
  <!-- Decorative elements -->
  <circle cx="60" cy="60" r="55" stroke="#D4AF37" stroke-width="2" fill="none" opacity="0.5"/>
  
  <defs>
    <linearGradient id="clientGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0E4D7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E8DCC6;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
