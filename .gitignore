# Fichiers de développement
*.log
*.tmp
*.temp
.DS_Store
Thumbs.db

# Archives de déploiement
*.zip
*.tar.gz

# Fichiers de sauvegarde
*.bak
*.backup
*~

# Dossiers de cache
.cache/
.temp/

# Fichiers IDE
.vscode/
.idea/
*.swp
*.swo

# Fichiers système
desktop.ini
.directory

# Fichiers de test locaux
test-local.html

# Variables d'environnement (si ajoutées plus tard)
.env
.env.local
.env.production

# Node modules (si ajoutés plus tard)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
